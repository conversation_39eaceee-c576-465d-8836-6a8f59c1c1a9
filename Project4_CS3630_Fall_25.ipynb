{"cells": [{"cell_type": "markdown", "metadata": {"id": "oYCJM0SYlGd9"}, "source": ["# CS 3630 Project 4\n", "\n", "## Outline\n", "\n", "1. Markov Localization\n", "2. Particle Filtering\n", "3. Comparing the two algorithms\n", "4. Path following using particle filtering\n", "5. Submission\n", "\n", "## Logistics\n", "\n", "1. Date range: 9/23/2025 12:30 Tuesday - 10/03/2025 23:59 Friday.\n", "2. Late Date: 10/05/2025 23:59 Sunday.\n", "3. Submission should be made to Gradescope. Please carefully read the submission guidelines.\n", "\n", "## General Tips\n", "\n", "1. Start early.\n", "2. Make a copy of the notebook on Colab before running. (File > Save a copy in drive)\n", "3. You will need to use the `numpy` library to complete the coding parts.\n", "4. Check out the table of contents by clicking the top icon on the left bar.\n", "5. **Read through every line of the notebook.**\n", "6. For coding questions, fill in the code cells before you run them.\n", "7. Have fun!\n", "\n", "Created by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, & <PERSON>.\n", "\n", "Want to learn more about particle filtering? Check out this here escape room: https://sites.google.com/view/particlefilter3630/home\n"]}, {"cell_type": "markdown", "metadata": {"id": "JDEL-TH04cvF"}, "source": ["# Submission Guidelines"]}, {"cell_type": "markdown", "metadata": {"id": "q21meSnXLLkq"}, "source": ["### REPORT\n", "\n", "The report will be completed and submitted through gradescope. The assignment is called \"Project 4 - Report\" We will not accept submissions that only have the report answers in the python notebook. We also will not accept submissions in any other forms, such as pdf, powerpoint, etc. Failure to adhere to this guideline will result in a 0 on the report!\n", "\n", "Please fill the Subjective TODOs in the report assignment on gradescope and upload this python notebook and the python file version to the \"Project 4 - Coding\" gradescope assignment."]}, {"cell_type": "markdown", "metadata": {"id": "e1glFeRv4VrM"}, "source": ["Once you have completed everything in this notebook, comment out all the lines with `create_slide_show()` and `show_trajectory()`, and download your notebook specifically as `project4.ipynb`. If you don't comment out the lines, the Gradescope autograder will throw errors. You must also submit the python version of the notebook. If in colab, you can download your notebook as a .py file. Please rename this file to `project4.py`.\n", "\n", "Files to be submitted:\n", "`project4.ipynb`,\n", "`project4.py`\n", "\n", "**_All answers to non-coding questions should be submitted to the \"Project 4 - Report\" online assignment on Gradescope. The notebook will not be graded._**\n"]}, {"cell_type": "markdown", "metadata": {"id": "SnSA5twiV_cI"}, "source": ["# Set-Up: Make sure you run the following cells every time you restart Colab runtime\n"]}, {"cell_type": "markdown", "metadata": {"id": "yz9ddR8YfvHk"}, "source": ["Install libraries which are necessary for this Notebook to run correctly. Feel free to check out each library.\n", "\n", "\n", "1.  [GtBook](https://github.com/gtbook/gtbook) - Responsible for installing helper libraries like GTSAM and Plotly\n", "2.  [<PERSON><PERSON><PERSON>](https://github.com/plotly/Kaleido) - Helps in generating static images and plots\n", "3.  [Ttictoc](https://github.com/hector-sab/ttictoc) - Helps measure the execution time for a block of code\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "o398zXilJMAP", "outputId": "c3d32553-d134-417a-f2d9-4da4e4c75540"}, "outputs": [], "source": ["%pip install -q -U gtbook\n", "%pip install -U ttictoc\n", "%pip install --upgrade plotly\n", "%pip install kaleido==0.2.1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We import libraries which are used in the Notebook\n", "\n", "\n", "1.   [NumPy](https://numpy.org/) - NumPy is an open source project aiming to enable numerical computing with Python.\n", "2.   [<PERSON><PERSON><PERSON>](https://plotly.com/python/) - <PERSON><PERSON>ly's Python graphing library makes interactive, publication-quality graphs\n", "3.   [GTSAM](https://gtsam.org/) - GTSAM is a sensor fusion library based on factor graphs.\n", "4.   [GtBook](https://github.com/gtbook/gtbook) - Responsible for installing helper libraries like GTSAM and Plotly\n", "5.   [Ttictoc](https://github.com/hector-sab/ttictoc) - Helps measure the execution time for a block of code"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IotmD5ZmJUL9"}, "outputs": [], "source": ["#export\n", "\n", "import numpy as np\n", "from numpy.random import default_rng\n", "\n", "rng = default_rng()\n", "\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "try:\n", "    import google.colab\n", "except:\n", "    import plotly.io as pio\n", "    pio.renderers.default = \"png\"\n", "\n", "import gtsam\n", "from gtbook import logistics\n", "from gtbook.display import show\n", "from IPython.display import clear_output\n", "from ttictoc import tic, toc\n"]}, {"cell_type": "markdown", "metadata": {"id": "AXf1VIVW5Cuu"}, "source": ["Here, we are seeding `np.random` to have uniformity while generating samples used later in the assignment:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tkijtidTiRtg"}, "outputs": [], "source": ["#export\n", "np.random.seed(0)"]}, {"cell_type": "markdown", "metadata": {"id": "0OhpCd-zf0Ws"}, "source": ["Firstly, we create a small ground truth trajectory. We generate this ground truth trajectory deterministically. \n", "\n", "We use the [gtsam.VectorValues](https://gtsam.org/doxygen/4.0.0/a03399.html) class to represent the trajectory; this will come in handy when we later simulate measurements.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3kyLsqJWJV03", "outputId": "48b455f3-028c-4ebb-c9b8-9562771b9632"}, "outputs": [], "source": ["#export\n", "left = [(10 + i * 5, 6) for i in range(9)]\n", "up = [(50, 6 + i * 5) for i in range(1, 6)]\n", "N = len(left) + len(up)\n", "indices = range(1, N + 1)\n", "x = {k: gtsam.symbol('x', k) for k in indices}\n", "values = gtsam.VectorValues()\n", "for i, state in enumerate(left + up):\n", "    values.insert(x[i + 1], state)\n", " # for each value of the variable, for instance (50, 26), the left number represents x coordinate, and the right number represents y coordinate\n", "ground_truth = np.array([values.at(x[k]) for k in indices])\n", "print(\"Ground truth trajectory index \\t X Coordinate\\tY Coordinate\")\n", "for index,i in enumerate(ground_truth):\n", "  print(\"{} \\t\\t\\t\\t {}\\t\\t{}\".format(index+1,i[0],i[1]))"]}, {"cell_type": "markdown", "metadata": {"id": "fPiKXc3LxHy1"}, "source": ["Finally, we overlaying the ground truth trajectory on a map of the warehouse introduced in the [book](https://www.roboticsbook.org/S43_logistics_sensing.html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "6AYjZN-UJgX3", "outputId": "4228ef0a-b6fc-4b48-fb14-078e83287807"}, "outputs": [], "source": ["#export\n", "logistics.show_map(logistics.base_map, ground_truth)"]}, {"cell_type": "markdown", "metadata": {"id": "coM0SsZkxnrq"}, "source": ["\n", "[Section 4.1](https://www.roboticsbook.org/S41_logistics_state.html) in the book has 2 broad approches representing uncertainity in a continuous state space. \n", "\n", "We start by initializing the finite element density representation with a prior, centered around the ground truth location, but with a relatively large standard deviation of 5 meter and then plot it"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "cYty9L5_JxeL", "outputId": "914d9f85-fb1d-41fb-b8d0-43a5ae1f52b6"}, "outputs": [], "source": ["#export\n", "prior_mean = values.at(x[1])\n", "prior_cov = np.diag([25, 25])\n", "prior = logistics.gaussian(logistics.map_coords, prior_mean, prior_cov)\n", "logistics.show_map(prior / np.max(prior) + 0.1 * logistics.base_map)\n"]}, {"cell_type": "markdown", "metadata": {"id": "KiHH2Ss1eCE4"}, "source": ["We define a Utility function called `create_slide_show()` to show animation on warehouse map, possibly with markers and robots."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PGAKFYweeARX"}, "outputs": [], "source": ["#export\n", "from skimage import io\n", "import os\n", "\n", "\n", "def create_slide_show():\n", "    img_list = []\n", "    k = 1\n", "    while True:\n", "        if not os.path.exists(\"{}.png\".format(k)):\n", "            # print(\"Images not found! Please run simulation to collect images first.\")\n", "            break\n", "        img = io.imread(\"{}.png\".format(k))\n", "        img_list.append(img)\n", "        os.remove(\"{}.png\".format(k))\n", "        k += 1\n", "\n", "    # If no images, don't do anything\n", "    if len(img_list) == 0:\n", "        return\n", "\n", "    img_list = np.array(img_list)\n", "    fig = px.imshow(img_list, animation_frame=0)\n", "    fig.update_layout(showlegend=False)\n", "    fig.update_xaxes(visible=False)\n", "    fig.update_yaxes(visible=False)\n", "    fig.show()"]}, {"cell_type": "markdown", "metadata": {"id": "rY3iG48rrZ0e"}, "source": ["The utility function, `show_trajectory()`, shows the image of the warehouse map, with the possibly of adding markers and robots."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rFMic9ryR2Eq"}, "outputs": [], "source": ["#export\n", "def show_trajectory(gts, ests):\n", "    fig = px.imshow(0.1 * logistics.base_map,\n", "                    color_continuous_scale='Oranges',\n", "                    origin=\"lower\",\n", "                    aspect='equal',\n", "                    range_color=[0, 1])\n", "\n", "    fig.add_scatter(x=gts[:, 0],\n", "                    y=gts[:, 1],\n", "                    mode=\"markers\",\n", "                    marker=dict(color=\"green\", opacity=1, size=10))\n", "    fig.add_scatter(x=ests[:, 0],\n", "                    y=ests[:, 1],\n", "                    mode=\"markers\",\n", "                    marker=dict(color=\"red\", opacity=1, size=10))\n", "\n", "    fig.update_layout(coloraxis_showscale=False,\n", "                      margin=dict(l=0, r=0, t=0, b=0))\n", "    fig.update_xaxes(range=[-0.5, 99.5], autorange=False)\n", "    fig.update_yaxes(range=[-0.5, 49.5], autorange=False, scaleratio=1)\n", "    fig.show()\n"]}, {"cell_type": "markdown", "metadata": {"id": "NZyHe2nmUZ1r"}, "source": ["# Part 1: Markov Localization [(Chapter 4.4.1 from the Book)](https://www.roboticsbook.org/S44_logistics_perception.html#markov-localization)\n"]}, {"cell_type": "markdown", "metadata": {"id": "pYs4ROTCzPvC"}, "source": ["Meet <PERSON>, a robot that was born with the gift of Markov localization. Markov localization is an algorithm that uses a continuous state representation when performing localization task. Since the code of Markov Localization is already given to you in the textbook, we will focus on understanding the code and parameters that are used in <PERSON>'s algorithm. For this section, you will tune <PERSON>'s parameters and record the results in the online report on Gradescope.\n"]}, {"cell_type": "markdown", "metadata": {"id": "uW9gnuXD3iJc"}, "source": ["![](https://drive.google.com/uc?export=view&id=1xbXUb59mSoPKmztWyIk4OUBccOeJMLK0)\n"]}, {"cell_type": "markdown", "metadata": {"id": "dojh88mbJhwX"}, "source": ["_Art: Meet <PERSON>_\n"]}, {"cell_type": "markdown", "metadata": {"id": "TqJ66WnAKtCo"}, "source": ["The hardest and most computationally demanding part in the Markov localization algorithm to implement is the prediction step. Recall the [formula](https://www.roboticsbook.org/S44_logistics_perception.html#warehouse-example):\n", "\n", ">> $P(X_{k}|\\mathcal{Z}^{k-1},\\mathcal{U}^{k})=\\sum_{x_{k-1}}P(X_{k}|x_{k-1},u_{k-1})P(x_{t-1}|\\mathcal{Z}^{k-1},\\mathcal{U}^{k-1})$\n", "\n", "\n", " \n", "Hence, for every cell in the predictive density approximation, we need to sum over all cells in the previous image. "]}, {"cell_type": "markdown", "metadata": {"id": "ism-1qPoKtCp"}, "source": ["We assume that the distribution of <PERSON>'s possible locations after the motion is gaussian with a mean value at each location that could possibly be reached from <PERSON>'s previous location and a covariance represented by the provided motion model sigma. Please read the code below to convince yourself that <PERSON>'s motion is not deterministic, but indeed probabilistic: "]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ryunOVKtKgYz"}, "outputs": [], "source": ["#export\n", "def prediction_step(previous, control, motion_model_sigma):\n", "    cov = np.eye(2) * motion_model_sigma**2\n", "\n", "    H, W = 50, 100\n", "    predictive_density = np.zeros((H, W))\n", "    for i in range(H):\n", "        for j in range(W):\n", "            if previous[i, j] > 1e-5:\n", "                previous_xy = logistics.map_coords[i, j]\n", "                mean = previous_xy + control\n", "                for k in range(H):\n", "                    motion_model = logistics.gaussian(logistics.map_coords[k],\n", "                                                      mean, cov)\n", "                    predictive_density[k] += motion_model * previous[i, j]\n", "    predictive_density = predictive_density / np.sum(predictive_density)\n", "    return predictive_density"]}, {"cell_type": "markdown", "metadata": {"id": "eW9dZmybHvHn"}, "source": ["In fact, <PERSON> could be at any position with non-zero probability, so you will need to help <PERSON> perform a motion update at each possible location under the Markov continuous state model. **_This could be a problem if the number of states is huge._** And in the current assignment, there are 5000 (50 \\* 100) cells in the grid map so there are 5000 states in total.\n"]}, {"cell_type": "markdown", "metadata": {"id": "y2p-LlFpJXaf"}, "source": ["![](https://drive.google.com/uc?export=view&id=13PRNBJ2-Naac-MnyDwb-d4bpxDj3wJZC)\n"]}, {"cell_type": "markdown", "metadata": {"id": "ouFlnq-6JcIA"}, "source": ["_Art: Where is <PERSON>?_\n"]}, {"cell_type": "markdown", "metadata": {"id": "D5k8VhBB4uu8"}, "source": ["The following code is the main logic loop, or the \"meat\" of Markov Localization. In this example, <PERSON> wants to move between location values `k` and `k+1`. We get the current probability densities using the function above first with the motion model. We will correct the probability density by multiplying the likelihood given by our sensors; this will give us the posterior density. Below you will find the code for running Markov localization.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cF8Wcr8aKjrD"}, "outputs": [], "source": ["#export\n", "def run_markov(prior,\n", "               indices,\n", "               control_values,\n", "               motion_model_sigma,\n", "               show_map=False):\n", "    \"\"\"\n", "      Refer to the code in the text book for predictive density and posterior density calculation.\n", "    \"\"\"\n", "    predictive_density = prior\n", "    posterior_density = predictive_density * logistics.proximity_map_off\n", "    posterior_density /= np.sum(posterior_density)\n", "    posterior_density_sequence = [posterior_density]\n", "    #Move from location 1->2->3->...\n", "    for k in indices[:-1]:\n", "        # prediction phase\n", "        control = control_values.at(x[k + 1]) - control_values.at(\n", "            x[k])  # ground truth control\n", "        predictive_density = prediction_step(posterior_density, control,\n", "                                             motion_model_sigma) \n", "        #Getting the current probability density\n", "        posterior_density = predictive_density * logistics.proximity_map_off \n", "        #We multiply probility density with sensor likilehoods and get posterior density\n", "        posterior_density /= np.sum(posterior_density)\n", "        #Appending the answer to a list\n", "        posterior_density_sequence.append(posterior_density)\n", "        if show_map:\n", "            logistics.show_map(posterior_density / np.max(posterior_density) +\n", "                               0.1 * logistics.base_map,\n", "                               file=\"{}.png\".format(k))\n", "            if k != indices[-2]:\n", "                clear_output(wait=True)\n", "    return posterior_density_sequence\n"]}, {"cell_type": "markdown", "metadata": {"id": "qmlQEy56HsLE"}, "source": ["### CODING TODO 1\n", "\n", "The above code doesn't really show <PERSON> moving - it just returns all the possible Manuel<PERSON> in the warehouse. There is only _one_ <PERSON> we are concerned about. Therefore, we have to use these probabilities to estimate where the real <PERSON> is.\n", "\n", "In the cell below: \n", "* Implement a function to figure out the estimated location from output posterior densities. \n", "* Use the weighted average of the locations based on the posterior densities. \n", "\n", "(Hint: use `np.average()` and you probably will need to flatten the posterior densities.)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7MWP2xXOIvvE"}, "outputs": [], "source": ["#export\n", "#Coding TODO 1\n", "def get_markov_location_best_estimate(posterior_density):\n", "    \"\"\"\n", "      posterior_density: 50 * 100 2d float array of posterior density\n", "      \n", "      Returns:\n", "        best_estimate: (float, float), which is a coordinate (the weighted average of the positions)\n", "    \"\"\"\n", "    positions = np.array([(j, i) for i in range(50) for j in range(100)])\n", "    best_estimate = None\n", "    ###############################################################################\n", "    #                             START OF YOUR CODE                              #\n", "    ###############################################################################\n", "    \n", "\n", "\n", "    ###############################################################################\n", "    #                              END OF YOUR CODE                               #\n", "    ###############################################################################\n", "    return best_estimate"]}, {"cell_type": "markdown", "metadata": {"id": "_gqmmTROKHKT"}, "source": ["#### CODING TODO 2\n", "\n", "In the next cell, calculate the average distance between the estimated locations and the ground truth, **and the averaged distance would be used as the error metric for the estimated locations in the assignment.** Low error value (shorter distance) means that the estimation is good.\n", "\n", "* Loop over the ground truth values and calculate the accuracies using root mean square distance. \n", "* Save your estimates in the best_estimates array and return with the average distance. You are using the euclidean distance to calculate the distance between the estimated locations and the ground truth locations. \n", "\n", "You will need to call the function you implemented above (CODING TODO 1).\n", "\n", "(Tip: use `np.linalg.norm`) \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WYZ9JAlINf2n"}, "outputs": [], "source": ["#export\n", "#Coding TODO 2\n", "def get_markov_location_error(posterior_density_sequence, ground_truth_values):\n", "    \"\"\"\n", "        Loop over the ground truth values and calculate the accuracies using root mean square distance.\n", "        Save your estimates in the best_estimates array and return with the average distance.\n", "\n", "        posterior_density_sequence: List[50 * 100 2d float array], list of posterior density for each step\n", "        ground_truth_values: List[(flost, float)], list of ground truth locations for each step\n", "\n", "        Returns:\n", "          average_distance: float, average distance from the estimated locations to the ground truth\n", "          best_estimates: List[(float, float)], list of location estimations along the trajectory \n", "            for each step with length equals to the ground_truth_values\n", "    \"\"\"\n", "    best_estimates = []\n", "    average_distance = 0.0\n", "    n = len(ground_truth_values)\n", "    ###############################################################################\n", "    #                             START OF YOUR CODE                              #\n", "    ###############################################################################\n", "    \n", "\n", "    \n", "    ###############################################################################\n", "    #                              END OF YOUR CODE                               #\n", "    ###############################################################################\n", "    # Return the average average_distance\n", "    return best_estimates, average_distance"]}, {"cell_type": "markdown", "metadata": {"id": "-T43rWgNribs"}, "source": ["In the next cell, we use the functions we defined above to run markov localization and measure the time taken. We also compute the average error in Markov localization using the get get_markov_location_error defined by you in CODING TODO 2."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 594}, "id": "EDaps08T6DiD", "outputId": "545f1983-43e5-43d2-871f-a08406c2e13e"}, "outputs": [], "source": ["#export\n", "tic()\n", "\n", "motion_model_sigma = 1.0 # Change sigma here\n", "posterior_density_sequence = run_markov(prior, indices, values, motion_model_sigma, show_map=True)\n", "\n", "gts = np.array([values.at(x[k]) for k in indices])\n", "best_estimates, average_distance = get_markov_location_error(posterior_density_sequence, gts)\n", "print(\"Average error: \", average_distance)\n", "\n", "print(f\"\\nWall time: {toc()} s\")"]}, {"cell_type": "markdown", "metadata": {"id": "fjQ3uG8L5057"}, "source": ["The following block is will create a slide show if the `show_map` was set to `True` in the previous block. \n", "\n", "**Comment this block before submitting to Gradescope.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "J2Gsyf7p6mcF", "outputId": "fecb9e70-a616-46bc-ed4b-bb5fa8a68c9c"}, "outputs": [], "source": ["#export\n", "\"\"\"\n", " If you have set show_map=True, run this cell to get the slide show \n", " after running the previous cell. Otherwise, don't run this cell!\n", " \n", " Comment this line before submitting to Gradescope.\n", "\"\"\"\n", "create_slide_show()"]}, {"cell_type": "markdown", "metadata": {"id": "HeSh8dfo51fL"}, "source": ["The following block is will show the estimated trajectory. Red - Estimated; Green - Ground Truth\n", "\n", "**Comment this block before submitting to Gradescope.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "kbqpv4BuSxX7", "outputId": "776f2c1e-f4bb-4c40-9fd2-6ce10befd64a"}, "outputs": [], "source": ["#export\n", "\"\"\"\n", "  Display the estimated trajectory compared with the ground truth.\n", "  Red is estimated, Green is ground truth\n", "  \n", "  Comment this line before submitting to Gradescope.\n", "\"\"\"\n", "show_trajectory(gts, np.array(best_estimates))"]}, {"cell_type": "markdown", "metadata": {"id": "q_GQXSm36LD4"}, "source": ["### REPORT TODO 1-2 \n", "\n", "Change the motion model sigma above to be `[1.0, 2.0, 3.0]`. Report the following in the corresponding report section on Gradescope:\n", "\n", "1. The error (average distance) of the algorithm for each sigma.\n", "2. The running time of the algorithm for sigma=1.0, which can be found as `Wall time` in the outputs of the cell with `tic()`. The result will be used in part 3. (Important: set `show_map=False` before measuring).\n"]}, {"cell_type": "markdown", "metadata": {"id": "IRH3OSVPXH3w"}, "source": ["### REPORT TODO 3\n", "\n", "Please provide an explanation for your observations. How does changing the motion sigma affect trajectory error and sample distribution as observed in the slide shows?\n"]}, {"cell_type": "markdown", "metadata": {"id": "zRn6oaNy6_4x"}, "source": ["# Part 2: Particle Filtering [(Section 4.4.2 in the Book)](https://www.roboticsbook.org/S44_logistics_perception.html#monte-carlo-localization)\n"]}, {"cell_type": "markdown", "metadata": {"id": "HPj-SzUS7F6a"}, "source": ["Now, meet <PERSON>. <PERSON> uses <PERSON> localization, or particle filtering. Particle filtering is similar to Markov localization, but different in the way it represents states: Markov localization represents state as a continuous space whereas <PERSON> localization (particle filtering) represents state as samples collected from the space of states.\n"]}, {"cell_type": "markdown", "metadata": {"id": "QIYGbDZJ3ulb"}, "source": ["![](https://drive.google.com/uc?export=view&id=1CeyVF3witiRfLtdvIEAZVGNUjlY90xoe)\n"]}, {"cell_type": "markdown", "metadata": {"id": "5uj_8-91KP7g"}, "source": ["_Art: Meet <PERSON>_\n"]}, {"cell_type": "markdown", "metadata": {"id": "A0Pic7ei8jDo"}, "source": ["Particle filtering first updates the samples of the motion model with likelihood weights from previous sensor observations. It does this by resampling the prior samples with prior weights and moving all the particles (samples) with the motion control instruction (motion update stage). Then, it calculates new likelihood weights based on current sensor observations for each particle (sensor update stage). The code below represents the process described.\n"]}, {"cell_type": "markdown", "metadata": {"id": "M3GRCbRj-seh"}, "source": ["<PERSON> uses 2 kinds of sensor readings to navigate her way around the warehouse:\n", "\n", "1. Proximity sensor, which turns on when <PERSON> is very close to an obstacle.\n", "2. RFID range sensor, which returns readings of type `(RFID_id, distance)` when <PERSON> is close to the RFID sensor. There are 8 fixed RFID sensors in the map at each end of the shelves. The distance measurement is assumed to have Gaussian noise. RFID_id is a unique id used to identify the sensor and the distance is the Gaussian Noise prone distance from the sensor.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0WW0DyWYLL5l"}, "outputs": [], "source": ["#export\n", "def likelihood_off(xy):\n", "    \"\"\"\n", "        Calculate likelihood by looking up value in proximity_map_off. \n", "        Returns whether the proximity sensor is off.\n", "    \"\"\"\n", "    #The following function calculates the correct cell for looking up the likelihood, and returns 0 likelihood if out of bounds\n", "    j, i = np.round(xy).astype(int)\n", "    if i < 0 or i > 49 or j < 0 or j > 99: return 0.0\n", "    return float(logistics.proximity_map_off[i, j])\n"]}, {"cell_type": "markdown", "metadata": {"id": "BOA1ziQXrskM"}, "source": ["<PERSON> has an RFID range sensor which tells it the distance from a particular RFID sensor with some Gaussian Noise. We can use this RFID measurement(sensor_id, distance) and calculate the likelihood of being at a particular place(x,y) in the warehouse. The function below takes in the rfid_measurement and the xy coordinates as a tuple as arguments and returns the likelihood that a given point matches the reading of an RFID device.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "To4BUyVDLXS_"}, "outputs": [], "source": ["#export\n", "def likelihood_range(xy, rfid_measurement):\n", "    \"\"\"\n", "        Calculate likelihood of xy given range measurement.\n", "        Assumes Gaussian noise of the RFID range result.\n", "        The function returns the likelihood that a given point matches the reading of an RFID device.\n", "    \"\"\"\n", "    _id, _range = rfid_measurement\n", "    if _id is None:\n", "        j, i = np.round(xy).astype(int)\n", "        if i < 0 or i > 49 or j < 0 or j > 99:\n", "            return 0.0\n", "        return float(logistics.out_of_bound_map[i, j])\n", "    else:\n", "        sigma = 1.0  # In meters\n", "        range_for_xy = logistics.rfid_range(xy, logistics.beacons[_id])\n", "        # Plug in the Gaussian noise\n", "        return 0.0 if range_for_xy is None else np.exp(\n", "            -1 / (2 * sigma**2) * (range_for_xy - _range)**2)\n"]}, {"cell_type": "markdown", "metadata": {"id": "C07jjhy661iP"}, "source": ["![](https://drive.google.com/uc?export=view&id=1dtJoZA1Qk24hcZaM8HLhJ8KYMi8lymgt)\n", "\n", "_Art: <PERSON> uses RFID to sense range_\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WXXsCvkDxLFX"}, "outputs": [], "source": ["#export\n", "def get_likelihood_off_weights(samples):\n", "    \"\"\"Calcuate the weight for each sample based on likelihood_off output\"\"\"\n", "    weights = np.apply_along_axis(likelihood_off, 1, samples)\n", "    return weights"]}, {"cell_type": "markdown", "metadata": {"id": "7TMe4Gnuruod"}, "source": ["The following function calculates the weight of each sample. It takes into account the likelihood_range output from the function that we defined previously"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "YGv4j9tJyVJd"}, "outputs": [], "source": ["#export\n", "def get_likelihood_range_weights(samples, position):\n", "    \"\"\"Calcuate the weight for each sample based on likelihood_range output \"\"\"\n", "    range_measurement = logistics.rfid_measurement(position)\n", "    weights = np.apply_along_axis(likelihood_range, 1, samples,\n", "                                  range_measurement)\n", "    return weights"]}, {"cell_type": "markdown", "metadata": {"id": "55AyyalRCUYa"}, "source": ["Below is the code that defines the motion update and the sensor update stages of particle filtering.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0mtDdN0DKoV-"}, "outputs": [], "source": ["#export\n", "def predict_samples(samples, weights, control, motion_model_sigma, size=500):\n", "    \"\"\"Create predictive density from weighted samples given control and control stddev. \"\"\"\n", "    prediction_samples = None\n", "    weights /= np.sum(weights)\n", "    # Resample\n", "    component_indices = rng.choice(len(samples), size=size, p=weights)\n", "    # Motion update\n", "    means = samples + control\n", "    # Inject Gaussian noise\n", "    cov = np.eye(2) * motion_model_sigma**2\n", "    prediction_samples = np.array(\n", "        [rng.multivariate_normal(means[s], cov) for s in component_indices])\n", "    return prediction_samples"]}, {"cell_type": "markdown", "metadata": {"id": "cN0DOS4lryHv"}, "source": ["<PERSON> uses the following code block to run particle filtering or Monte Carlo localization. This multiplies the two likelihoods in ever measurement update phase"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aLM3LoC5Noac"}, "outputs": [], "source": ["#export\n", "def run_particle_filter(samples,\n", "                        indices,\n", "                        weights,\n", "                        sigma,\n", "                        control_values,\n", "                        size=500,\n", "                        show_map=False):\n", "    sample_sequence = []\n", "    for k in indices[:-1]:\n", "        # Ground truth control instruction\n", "        control = control_values.at(x[k + 1]) - control_values.at(x[k])\n", "        # Predict samples based on control\n", "        samples = predict_samples(samples, weights, control, sigma, size)\n", "        sample_sequence.append((samples, weights))\n", "\n", "        # Calculate likelihood weights for next control update\n", "        weights = get_likelihood_off_weights(samples) \\\n", "            * get_likelihood_range_weights(samples, control_values.at(x[k+1]))\n", "\n", "        if show_map:\n", "            logistics.show_map(0.1 * logistics.base_map,\n", "                               markers=samples,\n", "                               marker=dict(color=\"red\",\n", "                                           opacity=0.1,\n", "                                           size=10 * weights /\n", "                                           np.max(weights)),\n", "                               file=\"{}.png\".format(k))\n", "            if k != indices[-2]:\n", "                clear_output(wait=True)\n", "    return sample_sequence"]}, {"cell_type": "markdown", "metadata": {"id": "y5OoHS6aXqZP"}, "source": ["### CODING TODO 3\n", "\n", "However, similar to the problem we ran into with <PERSON>, all we have is an array of possible Jennys. We are only concerned about estimating _one_ <PERSON>.\n", "\n", "In the below cell implement a function to figure out <PERSON>'s estimated location from the output samples. \n", "* Use the weighted average of the locations based on the samples. \n", "* Return a tuple of floats which is the weighted average of the sample coordinates\n", "\n", "(Hint: use `np.average`). \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MGVm-wCYTwTh"}, "outputs": [], "source": ["#export\n", "#Coding TODO 3\n", "def get_particle_filtering_best_estimate(samples, weights):\n", "    \"\"\"\n", "      samples: List[(float, float)], list of sample coordinates\n", "      weights: List[float], list of weight for each sample\n", "      \n", "      Returns:\n", "        best_estimate: (float, float), the average of the sample coordinates with weights=weights\n", "    \"\"\"\n", "    best_estimate = None\n", "    ###############################################################################\n", "    #                             START OF YOUR CODE                              #\n", "    ###############################################################################\n", "    \n", "\n", "\n", "    ###############################################################################\n", "    #                              END OF YOUR CODE                               #\n", "    ###############################################################################\n", "    return best_estimate"]}, {"cell_type": "markdown", "metadata": {"id": "-lLtorQkTzO4"}, "source": ["### CODING TODO 4\n", "In the next cell: \n", "\n", "* Use euclidean distance to calculate the distance between the estimated locations and the ground truth locations. \n", "* The averaged distance will be used as the error metric for the estimated locations in the assignment.\n", "* Low error value (shorter distance) means the estimation is good.\n", "\n", "(Tip: use `np.linalg.norm`) You will need to call the function you implemented above.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WWcQ70YWUFzh"}, "outputs": [], "source": ["#export\n", "# Coding TODO 4\n", "def get_particle_filtering_error(sample_sequence, ground_truth_values):\n", "    \"\"\" \n", "        Loop over the ground truth values and calculate the average distances using euclidean distance.\n", "        Save your estimates in the best_estimates array and return with the error.\n", "\n", "        sample_sequence: List[(float, float)], list of tuples of samples and weights for each step\n", "        ground_truth_values: List[(float, float)], list of ground truth locations for each step\n", "\n", "        Returns:\n", "          average_distance: float, average distance from the estimated locations to the ground truth\n", "          best_estimates: List[(float, float)], list of location estimations along the trajectory for each \n", "            step with length equals to the ground_truth_values\n", "    \"\"\"\n", "    average_distance = 0.0\n", "    best_estimates = []\n", "    n = len(ground_truth_values)\n", "    ###############################################################################\n", "    #                             START OF YOUR CODE                              #\n", "    ###############################################################################\n", "    \n", "\n", "    \n", "    ###############################################################################\n", "    #                              END OF YOUR CODE                               #\n", "    ###############################################################################\n", "    # Return the average error\n", "    return best_estimates, average_distance"]}, {"cell_type": "markdown", "metadata": {"id": "TJXf1RezCIlO"}, "source": ["**The next cell contains functions for initializing the samples. You will find them useful in a proceeding task.**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7vEIINQTLHtO"}, "outputs": [], "source": ["#export\n", "# uniform\n", "def get_uniform_distribution(num_samples):\n", "    return rng.uniform(low=gtsam.Point2(0, 0),\n", "                       high=gtsam.Point2(100, 50),\n", "                       size=(num_samples, 2))\n", "\n", "\n", "# node centered\n", "def get_node_centered_distribution(num_samples):\n", "    return rng.multivariate_normal(values.at(x[1]),\n", "                                   np.diag([25, 25]),\n", "                                   size=num_samples)\n", "\n", "\n", "# multi-modal distribution\n", "def get_multi_modal_distribution(num_samples):\n", "    prior_samples = []\n", "    points = [[10, 6], [80, 20], [45, 25], [20, 40]]\n", "    for i in range(4):\n", "        center = rng.multivariate_normal(points[i],\n", "                                         np.diag([25, 25]),\n", "                                         size=num_samples // 4)\n", "        prior_samples.append(center)\n", "    return np.concatenate(prior_samples, axis=0)\n"]}, {"cell_type": "markdown", "metadata": {"id": "ZAkVNkDXYk_u"}, "source": ["**Please alter the variable values in the cell below to complete the todos for this section.**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 594}, "id": "LKPUyZYw9_jt", "outputId": "763c9293-2881-49e6-bb9a-3e43eee5ab8f"}, "outputs": [], "source": ["#export\n", "tic()\n", "\n", "motion_model_sigma_pf = 1.0 # Alter in TODO 2.1\n", "sample_size = 500 # Alter in TODO 2.3\n", "samples = get_uniform_distribution(sample_size) # Alter in TODO 2.5\n", "\n", "# We could first calculate weights to filter out unlikely samples before we start\n", "weights = get_likelihood_off_weights(samples) \\\n", "            * get_likelihood_range_weights(samples, values.at(x[1]))\n", "sample_sequence = [(samples, weights)]\n", "ss = run_particle_filter(samples, indices, weights, motion_model_sigma_pf, values, sample_size, show_map=True)\n", "sample_sequence.extend(ss)\n", "\n", "gts = np.array([values.at(x[k]) for k in indices])\n", "pf_best_estimates, average_distance = get_particle_filtering_error(sample_sequence, gts)\n", "print(\"Average error: \", average_distance)\n", "\n", "print(f\"\\nWall time: {toc()} s\")"]}, {"cell_type": "markdown", "metadata": {"id": "qJ8x6tcBVJ6Z"}, "source": ["The following block is will create a slide show if the show_map was set to True in the previous block. \n", "\n", "**Comment this block before submitting to Gradescope.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "93hAFYzC-Wpx", "outputId": "77f8c5b8-8cc9-4abc-a254-28447fe50da8"}, "outputs": [], "source": ["\"\"\"\n", " If you have set show_map=True, run this cell to get the slide show \n", " after running the previous cell. Otherwise, don't run this cell!\n", " \n", " Comment this line before submitting to Gradescope.\n", "\"\"\"\n", "create_slide_show()"]}, {"cell_type": "markdown", "metadata": {"id": "ADexPsDxVLVQ"}, "source": ["The following block is will show the estimated trajectory. Red - Estimated; Green - Ground Truth\n", "\n", "**Comment this block before submitting to Gradescope.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "h6rIlVVNUOD-", "outputId": "8ddfbcad-95d9-43a3-c105-5ba2e9e516e7"}, "outputs": [], "source": ["\"\"\"\n", "  Display the estimated trajectory compared with ground truth.\n", "  Red is estimated, Green is ground truth\n", "\n", "  Comment this line before submitting to Gradescope.\n", "\"\"\"\n", "show_trajectory(gts, np.array(pf_best_estimates))"]}, {"cell_type": "markdown", "metadata": {"id": "pdncooMpFNaD"}, "source": ["### REPORT TODO 4\n", "\n", "**Set sample size back to `500` and the distribution back to uniform.** Change the motion model sigma above to be `[1.0, 2.0, 3.0]`. Report the average distance of the algorithm for each sigma in the corresponding report section on Gradescope.\n"]}, {"cell_type": "markdown", "metadata": {"id": "Owhg4IuXWynz"}, "source": ["### REPORT TODO 5 \n", "\n", "Please provide an explanation for your observations. \n", "* Does changing the motion sigma in particle filtering have similar effects on trajectory error (average distance) and sample distribution as Markov localization? \n", "* Why or why not?\n"]}, {"cell_type": "markdown", "metadata": {"id": "K9w6F0u5W0AA"}, "source": ["### REPORT TODO 6-7\n", "\n", "**Set the motion sigma back to `1.0` and the distribution back to uniform.** Change the sample size to be `[500, 1000, 2000, 5000, 10000]`. Report the following in the corresponding report section on GradeScope:\n", "\n", "1. The running time of the algorithm for each sample size, which can be found as `Wall time` in the outputs of the cell with `tic()`. (Important: set `show_map=False` before measuring).\n", "2. The average distance of the algorithm for each sample size.\n"]}, {"cell_type": "markdown", "metadata": {"id": "4lLm-evsW1Ge"}, "source": ["### REPORT TODO 8\n", "\n", "Please provide an explanation for your observations. How does changing the sample size affect running time and error (average distances)?\n"]}, {"cell_type": "markdown", "metadata": {"id": "Oh4oGZLi6yjb"}, "source": ["![](https://drive.google.com/uc?export=view&id=1tOA9_3guARWJ-KITEKZiEV8dgOWXNt2m)\n", "\n", "_Art: <PERSON> wonders - what is the best sample size?_\n"]}, {"cell_type": "markdown", "metadata": {"id": "T0jNL5GFY28M"}, "source": ["### REPORT TODO 9\n", "\n", "**Set motion sigma back to `1.0` and sample size back to `500`.** Change the sample initialization method above to uniform, multimodal, and node-centered. Report the following in the corresponding report section on GradeScope:\n", "\n", "1. Report the error (average distance) of the algorithm given each distribution.\n"]}, {"cell_type": "markdown", "metadata": {"id": "PwtgvaKZCx-l"}, "source": ["# Part 3: Compare the Two Algorithms\n"]}, {"cell_type": "markdown", "metadata": {"id": "8DZ2yGb8lZId"}, "source": ["![](https://drive.google.com/uc?export=view&id=1iOQXekxIXghwtom7WvtM1jGk0GR-vIA-)\n", "\n", "_Art: <PERSON> \"<PERSON><PERSON>\" <PERSON> makes a bet... \\$v\\$_\n"]}, {"cell_type": "markdown", "metadata": {"id": "vO-v99ygMX3y"}, "source": ["<PERSON> \"Ka<PERSON>\" <PERSON> has a bet with <PERSON> \"Trial-and-Error\" <PERSON> that <PERSON> can move boxes faster than <PERSON>. The bet is worth 5 dollars. You can use the parameters from Part 1 and 2 to help <PERSON> and <PERSON> decide who gets the 5 dollars with the magic of graphs! You will plot two graphs and upload screenshots onto the Project 4 Gradescope report. **No coding or parameter changing is needed for this part.**\n"]}, {"cell_type": "markdown", "metadata": {"id": "pVP8jw3WNFKD"}, "source": ["**Your results in TODO 1-2 (the running time and average distance of Markov Localization with motion sigma=1.0) and TODO 6-7 (running time and average distances of Particle Filtering with different sample sizes) will be used in this part for plotting the graphs.**\n"]}, {"cell_type": "markdown", "metadata": {"id": "y3pzafBZavc5"}, "source": ["### REPORT TODO 10\n", "\n", "Plot a graph with **sample size** on the x-axis and **running time** on the y-axis. We would like to compare the running time of particle filtering to that of Markov localization. Since the number of states in Markov localization in this assignment is always 5000 (equivalent to 5000 static samples), you should plot a horizontal line showing the running time of Markov localization with sigma of `1.0`. Then, plot the running time of particle filtering at each sample size. Connect each point with a line if possible. You can use any plotting tool.\n", "\n", "After plotting, answer the following:\n", "\n", "Describe the trend between running time and sample size for particle filtering. Approximately at which sample size will the running time of particle filtering reach that of Markov localization?"]}, {"cell_type": "markdown", "metadata": {"id": "8FoTZC2V7oXL"}, "source": ["![](https://drive.google.com/uc?export=view&id=1hcZpf2rqbF6rmDb6CbOfCsgNu6QBg0dW)\n", "\n", "_Art: Robot too tired to run experiments_\n"]}, {"cell_type": "markdown", "metadata": {"id": "pdFY-z2KcncE"}, "source": ["Particle Filtering is generally faster than Markov localization. But what about error? In the next TODO, you will compare the error of the two algorithms."]}, {"cell_type": "markdown", "metadata": {"id": "8e9SXbq8c0m4"}, "source": ["### REPORT TODO 11\n", "\n", "Plot a graph with **sample size** on the x-axis and **error** (average distance) on the y-axis. Again, please plot a horizontal line showing the average distance of Markov localization with sigma of `1.0`. Then, plot the scatter points of the error (average distance) of particle filtering at each different sample size. Connect each point with a line if possible. You can use any plotting tool.\n", "\n", "After plotting, answer the following:\n", "\n", "Describe the trend between error (average distance) and sample size for particle filtering. How does the error (average distances) of the two algorithms compare? At what sample size is the the error (average distance) of particle filtering similar to that of Markov localization?\n"]}, {"cell_type": "markdown", "metadata": {"id": "b_S-JWEfW5Wo"}, "source": ["### REPORT TODO 12\n", "\n", "Explain under what configuration (accuracy, sample size etc.) would you prefer particle filtering over Markov localization and vice versa?\n"]}, {"cell_type": "markdown", "metadata": {"id": "fkdJhKWKDcLU"}, "source": ["# Part 4: Path following using Particle Filtering\n"]}, {"cell_type": "markdown", "metadata": {"id": "rJu7_rYyEnTT"}, "source": ["Assume that <PERSON> is randomly placed into a map and has no idea where she is. She has some knowledge about the map and can obtain sensor data and motion data. Per usual, she will use Monte Carlo localization to estimate her location with proper control. The initial estimated location will be the mean of the randomly initialized samples. As in the next TODO, you will find out that motion in the map along with Monte Carlo localization will help <PERSON>'s estimated location converge to a point close to the ground truth.\n"]}, {"cell_type": "markdown", "metadata": {"id": "1L667Xgr340X"}, "source": ["![](https://drive.google.com/uc?export=view&id=1pBWAvoRAlXMRXc9MY2L5enq9PBPJkVFp)\n", "\n", "_Art: <PERSON> figures out her location_\n"]}, {"cell_type": "markdown", "metadata": {"id": "quWr8JkacCZh"}, "source": ["The `show_map_with_robot()` function below is an utility function for displaying the map of the warehouse and position of the robot in it using markers. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0BurcFd_IXIm"}, "outputs": [], "source": ["#export\n", "def show_map_with_robot(image=None,\n", "                        markers=None,\n", "                        robot=None,\n", "                        display=False,\n", "                        file: str = None,\n", "                        marker={}):\n", "    \"\"\" Utility function to show image on warehouse map, possibly with markers and robots \"\"\"\n", "    fig = px.imshow(image,\n", "                    color_continuous_scale='Oranges',\n", "                    origin=\"lower\",\n", "                    aspect='equal',\n", "                    range_color=[0, 1])\n", "    fig.add_scatter(x=[int(robot[0])],\n", "                    y=[int(robot[1])],\n", "                    mode=\"markers\",\n", "                    marker=dict(color=\"green\", opacity=1, size=10))\n", "    if markers is not None:\n", "        fig.add_scatter(x=markers[:, 0],\n", "                        y=markers[:, 1],\n", "                        mode=\"markers\",\n", "                        marker=marker)\n", "\n", "    fig.update_layout(coloraxis_showscale=False,\n", "                      margin=dict(l=0, r=0, t=0, b=0))\n", "    fig.update_xaxes(range=[-0.5, 99.5], autorange=False)\n", "    fig.update_yaxes(range=[-0.5, 49.5], autorange=False, scaleratio=1)\n", "    if display:\n", "        fig.show()\n", "    if file is not None:\n", "        fig.write_image(file)"]}, {"cell_type": "markdown", "metadata": {"id": "DgphP0wFo4p-"}, "source": ["## Warehouse Navigator ([Book Section 4.4.2.3](https://www.roboticsbook.org/S44_logistics_perception.html#mcl-warehouse-example))\n", "\n", "\n", "The robot navigates the warehouse with the help of the **WarehouseNavigator** class. It resets its ground truth to a different position or updates it as an offset from the current ground truth using controls in all four directions left, right, up, and down. The updated location of the robot is estimated with the `get_particle_filtering_best_estimate()` function that you already completed in Part 1. Given a start and target location, the robot follows a straight path within the warehouse in a particular direction using particle filtering. \n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "dm5ih3-hVfED"}, "source": ["### CODING TODO 5,6\n", "\n", "There are two coding TODOs you are required to complete as follows:\n", "#### Coding TODO 5: \n", "* The robot should not go out of bounds and stop within 1.0 of the target. Determine the stopping criteria of the robot.\n", "\n", "  **Hints:**\n", "  - Use `likelihood_off()` to detect out of bounds. \n", "  - Use `np.linalg.norm` to calculate distance.\n", "  \n", "#### Coding TODO 6:  \n", "* Determine the control of the robot in the specified direction towards the target while the stopping criteria does not satisfy."]}, {"cell_type": "markdown", "metadata": {"id": "KkrCrmzQu7PW"}, "source": ["For your convenience, the TAs have already completed a majority of this code in the following class definition:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "A9Zdabbou7PV"}, "outputs": [], "source": ["#export\n", "CONTROLS = {\n", "    'UP': (0, 2),\n", "    'DOWN': (0, -2),\n", "    'LEFT': (-2, 0),\n", "    'RIGHT': (2, 0)\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "i49onIQ9GAZW"}, "outputs": [], "source": ["#export\n", "class CrippledNavigator():\n", "    \"\"\"All the code that the TAs wrote to make your life easy for navigating a warehouse.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.ground_truth = np.array([0, 0]).astype(np.float64)\n", "        self.motion_sigma = 1\n", "        self.sample_size = 2000\n", "\n", "    def reset_ground_truth(self, point):\n", "        self.ground_truth = np.array(point).astype(np.float64)\n", "\n", "    def update_ground_truth(self, control):\n", "        self.ground_truth += np.array(control).astype(np.float64)\n", "\n", "    def get_rfid_measurement(self):\n", "        return logistics.rfid_measurement(self.ground_truth)\n", "\n", "    def save_map(self, index, samples, weights):\n", "        show_map_with_robot(0.1 * logistics.base_map,\n", "                            markers=samples,\n", "                            marker=dict(color=\"red\",\n", "                                        opacity=0.1,\n", "                                        size=10 * weights / np.max(weights)),\n", "                            robot=self.ground_truth,\n", "                            display=False,\n", "                            file=\"{}.png\".format(index))\n", "\n", "    def stopping_criteria(self, estimated_location, target):\n", "        \"\"\"You have to override this method in the derived class.\"\"\"\n", "        raise RuntimeError(\"You have to override 'stopping_criteria' in the derived class.\")\n", "\n", "    def update_estimate(self, samples, weights):\n", "        \"\"\" Return the estimated location using the function you filled before. \"\"\"\n", "        return get_particle_filtering_best_estimate(samples, weights)\n", "\n", "    def choose_control(self, estimated_location, target):\n", "        \"\"\"You have to override this method in the derived class.\"\"\"\n", "        raise RuntimeError(\"You have to override 'choose_control' in the derived class.\")\n", "\n", "    def follow_straight_path(self,\n", "                             start,\n", "                             target,\n", "                             direction='LEFT',\n", "                             prior_samples=None,\n", "                             save_map=False):\n", "        \"\"\"\n", "        Follows a straight path with given start and target in a specified direction. \n", "        Provide a prior_samples if you would like a pre-initialized sample distribution.\n", "\n", "        Parameters:\n", "          start (float, float): start location of the robot\n", "          target (float, float): target location of the robot\n", "          direction (str): specified direction\n", "          prior_samples (List[(float, float)]): list of sample coordinates for pre-initalization \n", "          save_map (bool): indicator to save map \n", "\n", "        Returns:\n", "          estimated_locations (List[(float, float)]): list of estimated_locations for each step\n", "          gts (List[(float, float)]): list of ground truths for each step\n", "          samples (List[(float, float)]): list of samples in the last step of the while loop\n", "    \"\"\"\n", "        self.reset_ground_truth(start)\n", "        init_rfid_measurement = self.get_rfid_measurement()\n", "        samples = get_uniform_distribution(\n", "            self.sample_size) if prior_samples is None else prior_samples\n", "        if save_map:\n", "            self.save_map(0, samples,\n", "                          np.ones(self.sample_size) / self.sample_size)\n", "\n", "        index = 0\n", "        weights = get_likelihood_off_weights(samples) \\\n", "                * get_likelihood_range_weights(samples, start)\n", "\n", "        estimated_location = self.update_estimate(samples, weights)\n", "        estimated_locations = [estimated_location]\n", "        gts = [self.ground_truth.copy()]\n", "\n", "        while not self.stopping_criteria(estimated_location, target):\n", "            control = self.choose_control(direction)\n", "            self.update_ground_truth(control)\n", "            samples = predict_samples(samples,\n", "                                      weights,\n", "                                      control,\n", "                                      self.motion_sigma,\n", "                                      size=self.sample_size)\n", "            rfid_measurement = self.get_rfid_measurement()\n", "            weights = get_likelihood_off_weights(\n", "                samples) * get_likelihood_range_weights(\n", "                    samples, self.ground_truth)\n", "\n", "            estimated_location = self.update_estimate(samples, weights)\n", "            estimated_locations.append(estimated_location)\n", "            gts.append(self.ground_truth.copy())\n", "            index += 1\n", "            if save_map:\n", "                self.save_map(index, samples, weights)\n", "        return estimated_locations, gts, samples\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "G_4run-bu7PX"}, "outputs": [], "source": ["#export\n", "class WarehouseNavigator(CrippledNavigator):\n", "    \"\"\"With your code the WarehouseNavigator will work.\"\"\"\n", "    \n", "    def stopping_criteria(self, estimated_location, target):\n", "        \"\"\"\n", "        Determines the stopping criteria for the robot.\n", "\n", "        Parameters:\n", "          estimated_location (float, float): estimated location of the robot\n", "          target (float, float): target location of the robot\n", "\n", "        returns a boolean for whether or not the robot must stop\n", "        \"\"\"\n", "        ###############################################################################\n", "        #                             START OF YOUR CODE                              #\n", "        ###############################################################################\n", "        \n", "\n", "\n", "        ###############################################################################\n", "        #                              END OF YOUR CODE                               #\n", "        ###############################################################################\n", "\n", "        return None\n", "        \n", "    def choose_control(self, direction):\n", "        \"\"\"\n", "        Determines the best control out of the four available.\n", "\n", "        Parameter:\n", "          direction (str): specified direction\n", "\n", "        returns the control for the given direction (float, float)\n", "        \"\"\"\n", "        ###############################################################################\n", "        #                             START OF YOUR CODE                              #\n", "        ###############################################################################\n", "        \n", "\n", "\n", "        ###############################################################################\n", "        #                              END OF YOUR CODE                               #\n", "        ###############################################################################\n", "\n", "        return None"]}, {"cell_type": "markdown", "metadata": {"id": "R0GhMelaRiL5"}, "source": ["The following code block calculates the **average error** between the estimated locations of the robot along the straight path obtained from the **WarehouseNavigator** and the ground truths given start and target points."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eIryZL_6lKYi", "outputId": "c8b47344-b442-4601-a29d-07b7cc053352"}, "outputs": [], "source": ["#export\n", "# Way points\n", "starting_point = [40, 7]\n", "destination = [60, 7]\n", "navigator = WarehouseNavigator()\n", "estimated_locations, line_gts, _ = navigator.follow_straight_path(starting_point,\n", "                                                       destination,\n", "                                                       'RIGHT',\n", "                                                       save_map=True)\n", "average_distance = np.sum(\n", "    [np.linalg.norm(estimated_location - gt)\n", "     for estimated_location, gt in zip(estimated_locations, line_gts)]) / len(line_gts)\n", "print(\"Average error: \", average_distance)"]}, {"cell_type": "markdown", "metadata": {"id": "b5H9grIKYA_s"}, "source": ["The following block is will create a slide show if the show_map was set to True in the previous block. \n", "\n", "**Comment this block before submitting to Gradescope.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "Bh7YL6gAhYLJ", "outputId": "79c09275-6dfd-4919-ae3c-8d5d2f642691"}, "outputs": [], "source": ["\"\"\"\n", " If you have set show_map=True, run this cell to get the slide show \n", " after running the previous cell. Otherwise, don't run this cell!\n", " \n", " Comment this line before submitting to Gradescope.\n", "\"\"\"\n", "create_slide_show()"]}, {"cell_type": "markdown", "metadata": {"id": "8__R_LdFVnbe"}, "source": ["The following block is will show the estimated trajectory. Red - Estimated; Green - Ground Truth\n", "\n", "**Comment this block before submitting to Gradescope.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "XxZtje-9haBj", "outputId": "21214d58-9917-43ac-ea71-ab419307209a"}, "outputs": [], "source": ["\"\"\"\n", "  Display the estimated trajectory compared with ground truth.\n", "  Red is estimated, Green is ground truth\n", "  \n", "  Comment this line before submitting to Gradescope.\n", "\"\"\"\n", "show_trajectory(np.array(line_gts), np.array(estimated_locations))"]}, {"cell_type": "markdown", "metadata": {"id": "JXL2RpXcIfJl"}, "source": ["If your implementation is correct, you should see the red sample points converge to the point of the robot (black dot in the map) after a few steps in the slide show. Sometimes, if the initialization is good enough, you'll find the estimated location close to the ground truth after the first step. The estimated trajectory (red dots) would be close to the ground truth (green dots).\n"]}, {"cell_type": "markdown", "metadata": {"id": "sC1J7hPTo2PF"}, "source": ["### REPORT TODO 13\n", "\n", "Insert a screenshot of the trajectory in the Gradescope report and report the error (average distance).\n"]}, {"cell_type": "markdown", "metadata": {"id": "SNwHLcYTw-Nb"}, "source": ["## 4.2 A More Complicated Path\n", "\n", "Based on the previous example, you will create an advanced warehouse navigator to follow a path with multiple waypoints! Notice that the path could be decomposed into multiple `follow_straight_path()`executions to reach the final destination, since between each pair of waypoints lies a straight path. You will implement the function `follow_path_with_waypoints()` and observe the final distance to the destination. If your implementation for `follow_straight_path()` is correct, the robot should stop close to the waypoints, and the distance to every waypoint when stopping is within `1.0` based on stopping criteria in the `stopping_criteria()` function.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CXd30J2hw91M"}, "outputs": [], "source": ["#export\n", "# Waypoints\n", "waypoints = [[30, 44], [50, 44], [50, 6], [66, 6], [66, 44], [80, 44]]"]}, {"cell_type": "markdown", "metadata": {"id": "X6KH5tzNVuCX"}, "source": ["#### CODING TODO 7\n", "\n", "Complete the last coding TODO of the project!\n", "\n", "Use `follow_straight_path()` to complete this function. \n", "* You could assume that the robot's ground truth location is at the first waypoint. \n", "* The robot will always move in the 4 directions defined in the class, and the waypoints will always be in the 4 directions related to each other. \n", "* Start by iterating through the waypoints. \n", "* You could use any function defined in this class and its super-class.\n", "\n", "**Remember:**\n", "- Set save_map=False when running `follow_straight_path()`.\n", "- When calling self.get_direction, use the given way points.\n", "- Update all the variables(est_pts, traj_gts, prev_waypoint, prev_sample) after each loop of `follow_straight_path()`.\n", "- Use the list `extend()` method.\n", "- The navigation could run for a while. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2W9pvvqGyVIY"}, "outputs": [], "source": ["#export\n", "class AdvancedWarehouseNavigator(WarehouseNavigator):\n", "\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def get_direction(self, start, end):\n", "        \"\"\" Return the estimated direction of the straight path as a string. \"\"\"\n", "        if start[0] == end[0]:\n", "            if start[1] > end[1]:\n", "                return 'DOWN'\n", "            elif start[1] < end[1]:\n", "                return 'UP'\n", "        else:\n", "            if start[0] > end[0]:\n", "                return 'LEFT'\n", "            elif start[0] < end[0]:\n", "                return 'RIGHT'\n", "\n", "    def follow_path_with_waypoints(self, waypoints):\n", "        \"\"\"\n", "        Follows a path with multiple waypoints where a straight path exists between each pair of them.\n", "\n", "        Parameters:\n", "          waypoints List[(float, float)]: a list of waypoints\n", "\n", "        Returns: \n", "          est_pts List[(float, float)]: a list of your estimated position along the trajectory\n", "          gts List[(float, float)]: a list of ground truths along the trajectory\n", "        \"\"\"\n", "        est_pts = []  # Estimated locations along the full trajectory\n", "        traj_gts = []  # Ground truth lcoations along the full trajectory\n", "        prev_waypoint = waypoints[0]  # Useful for calculating direction\n", "        prev_sample = None  # Set prior_samples=prev_sample when calling follow_straight_path\n", "        ###############################################################################\n", "        #                             START OF YOUR CODE                              #\n", "        ###############################################################################\n", "        \n", "        ###############################################################################\n", "        #                              END OF YOUR CODE                               #\n", "        ###############################################################################\n", "        return est_pts, traj_gts\n"]}, {"cell_type": "markdown", "metadata": {"id": "4ji9li7Qax3w"}, "source": ["The following code block calculates the **average error** between the estimated locations of the robot along the path obtained from the **AdvancedWarehouseNavigator** and the ground truths given multiple waypoints."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bQv6IJTk5tZg", "outputId": "23457557-8ed4-4b89-a789-a4a6709cda43"}, "outputs": [], "source": ["#export\n", "adv_navigator = AdvancedWarehouseNavigator()\n", "est_pts, traj_gts = adv_navigator.follow_path_with_waypoints(waypoints)\n", "average_distance = np.sum(\n", "    [np.linalg.norm(estimated_location - gt)\n", "     for estimated_location, gt in zip(estimated_locations, traj_gts)]) / len(traj_gts)\n", "print(\"Average distance: \", average_distance)"]}, {"cell_type": "markdown", "metadata": {"id": "XC-EgB1CVqly"}, "source": ["The following block is will show the estimated trajectory. Red - Estimated; Green - Ground Truth\n", "\n", "**Comment this block before submitting to Gradescope.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542}, "id": "AT8CtipH5cnz", "outputId": "4bcb6d1f-3834-417e-ce7b-c7be2a1907b7"}, "outputs": [], "source": ["\"\"\"\n", "  Display the estimated trajectory compared with ground truth.\n", "  Red is estimated, Green is ground truth\n", "  \n", "  Comment out this line before submitting to Gradescope.\n", "\"\"\"\n", "show_trajectory(np.array(traj_gts), np.array(est_pts))"]}, {"cell_type": "markdown", "metadata": {"id": "Uy_ih4gMEpvw"}, "source": ["If your implementation is correct, you must see the estimated locations (red dots) and ground truths (green dots) close to each other ie. both the trajectories must follow the same path. "]}, {"cell_type": "markdown", "metadata": {"id": "X4_sWoygorvN"}, "source": ["### REPORT TODO 14\n", "\n", "Insert a screenshot of the trajectory into the report on Gradescope and report the error (average distance).\n"]}], "metadata": {"colab": {"collapsed_sections": [], "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "add85e29415863f21e1294181f7c82a66a9fd3d90af82bfb33c40458868e4613"}}}, "nbformat": 4, "nbformat_minor": 0}